/**
 * Example usage of the normalizeMarkdownContent function
 */

import { normalizeMarkdownContent, RuleNormalizationError } from '../lib/utils';

async function exampleUsage() {
  console.log('=== normalizeMarkdownContent Examples ===\n');

  // Example 1: Basic markdown with frontmatter
  console.log('1. Basic markdown with frontmatter:');
  try {
    const basicExample = `---
title: "React Component Generator"
description: "Generate TypeScript React components with best practices"
applyType: "manual"
ideType: "CURSOR"
visibility: "PUBLIC"
tags: ["react", "typescript", "components"]
author: "<PERSON>"
---

# React Component Generator

> Generate TypeScript React components with best practices

## Instructions

When creating React components:

1. Use TypeScript with proper type definitions
2. Follow React best practices and hooks
3. Include proper error handling
4. Add JSDoc comments for props
5. Use modern React patterns (functional components, hooks)
6. Ensure accessibility with proper ARIA attributes

## Example Structure

\`\`\`tsx
interface ComponentProps {
  title: string;
  onClick?: () => void;
}

/**
 * A reusable button component
 */
export function Button({ title, onClick }: ComponentProps) {
  return (
    <button onClick={onClick} aria-label={title}>
      {title}
    </button>
  );
}
\`\`\`
`;

    const result1 = await normalizeMarkdownContent(basicExample);
    console.log('✅ Parsed successfully!');
    console.log('Title:', result1.metadata.title);
    console.log('Apply Type:', result1.metadata.applyType);
    console.log('IDE Type:', result1.metadata.ideType);
    console.log('Tags:', result1.metadata.tags);
    console.log('Content length:', result1.content.length, 'characters\n');
  } catch (error) {
    console.error('❌ Error:', error);
  }

  // Example 2: Markdown without frontmatter (metadata extracted from content)
  console.log('2. Markdown without frontmatter:');
  try {
    const contentExample = `# CSS Best Practices

> Guidelines for writing maintainable CSS

## Metadata

- **IDE Type:** GENERAL
- **Author:** Jane Smith
- **Created:** 8/3/2025

## Guidelines

1. Use semantic class names
2. Follow BEM methodology
3. Avoid deep nesting
4. Use CSS custom properties for theming
5. Optimize for performance

## Example

\`\`\`css
.card {
  --card-padding: 1rem;
  --card-border-radius: 0.5rem;
  
  padding: var(--card-padding);
  border-radius: var(--card-border-radius);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card__title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}
\`\`\`
`;

    const result2 = await normalizeMarkdownContent(contentExample, {
      defaultApplyType: 'always',
      defaultVisibility: 'PUBLIC'
    });
    console.log('✅ Parsed successfully!');
    console.log('Title:', result2.metadata.title);
    console.log('Description:', result2.metadata.description);
    console.log('IDE Type:', result2.metadata.ideType);
    console.log('Author:', result2.metadata.author);
    console.log('Apply Type:', result2.metadata.applyType);
    console.log('Visibility:', result2.metadata.visibility, '\n');
  } catch (error) {
    console.error('❌ Error:', error);
  }

  // Example 3: Error handling - missing required fields
  console.log('3. Error handling - missing title:');
  try {
    const invalidExample = `---
description: "A rule without a title"
applyType: "manual"
---

## Some Content

This rule is missing a title, which should cause validation to fail.
`;

    await normalizeMarkdownContent(invalidExample);
    console.log('❌ Should have thrown an error!');
  } catch (error) {
    if (error instanceof RuleNormalizationError) {
      console.log('✅ Correctly caught validation error:', error.message, '\n');
    } else {
      console.error('❌ Unexpected error:', error);
    }
  }

  // Example 4: Skip validation
  console.log('4. Skip validation for incomplete content:');
  try {
    const incompleteExample = `Some markdown content without proper structure.

This might be a work in progress.
`;

    const result4 = await normalizeMarkdownContent(incompleteExample, {
      validateRequired: false,
      defaultApplyType: 'manual'
    });
    console.log('✅ Parsed with validation disabled!');
    console.log('Title:', result4.metadata.title || '(empty)');
    console.log('Apply Type:', result4.metadata.applyType);
    console.log('Content preview:', result4.content.substring(0, 50) + '...\n');
  } catch (error) {
    console.error('❌ Error:', error);
  }

  // Example 5: Working with Blob (as mentioned in the requirements)
  console.log('5. Creating Blob from normalized content:');
  try {
    const ruleContent = `---
title: "API Documentation Rule"
description: "Guidelines for writing API documentation"
applyType: "auto"
---

# API Documentation Rule

> Guidelines for writing clear and comprehensive API documentation

## Requirements

1. Include request/response examples
2. Document all parameters
3. Specify error codes
4. Provide authentication details
`;

    const normalized = await normalizeMarkdownContent(ruleContent);
    
    // Create a Blob with the processed content (useful for downloads, etc.)
    const blob = new Blob([normalized.content], { type: 'text/markdown' });
    console.log('✅ Created Blob successfully!');
    console.log('Blob size:', blob.size, 'bytes');
    console.log('Blob type:', blob.type);
    console.log('Rule title:', normalized.metadata.title);
    console.log('Rule apply type:', normalized.metadata.applyType, '\n');
  } catch (error) {
    console.error('❌ Error:', error);
  }

  console.log('=== Examples completed ===');
}

// Run examples if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  exampleUsage().catch(console.error);
}

export { exampleUsage };
