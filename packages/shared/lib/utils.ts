/**
 * Shared utility functions across OnlyRules packages
 */

import { clsx, type ClassValue } from 'clsx';
import { IDE_TYPES } from './constants';
import type { IDEType, RuleFilters } from './types';

/**
 * Utility function to combine class names (similar to the existing utils)
 */
export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

/**
 * Format IDE type for display
 */
export function formatIDEType(ideType: IDEType): string {
  const formatMap: Record<IDEType, string> = {
    CURSOR: 'Cursor',
    AUGMENT: 'Augment Code',
    WINDSURF: 'Windsurf',
    CLAUDE: 'Claude',
    GITHUB_COPILOT: 'GitHub Copilot',
    GEMINI: 'Gemini',
    OPENAI_CODEX: 'OpenAI Codex',
    CLINE: 'Cline',
    JUNIE: 'Junie',
    TRAE: 'Trae',
    LINGMA: 'Lingma',
    KIRO: '<PERSON><PERSON>',
    TENCENT_CODEBUDDY: 'Tencent CodeBuddy',
    GENERAL: 'General',
  };
  
  return formatMap[ideType] || ideType;
}

/**
 * Get IDE type color for UI display
 */
export function getIDETypeColor(ideType: IDEType): string {
  const colorMap: Record<IDEType, string> = {
    CURSOR: '#007ACC',
    AUGMENT: '#FF6B35',
    WINDSURF: '#00D4AA',
    CLAUDE: '#FF8C00',
    GITHUB_COPILOT: '#24292E',
    GEMINI: '#4285F4',
    OPENAI_CODEX: '#412991',
    CLINE: '#8B5CF6',
    JUNIE: '#EC4899',
    TRAE: '#10B981',
    LINGMA: '#F59E0B',
    KIRO: '#EF4444',
    TENCENT_CODEBUDDY: '#06B6D4',
    GENERAL: '#6B7280',
  };
  
  return colorMap[ideType] || '#6B7280';
}

/**
 * Validate IDE type
 */
export function isValidIDEType(ideType: string): ideType is IDEType {
  return Object.keys(IDE_TYPES).includes(ideType as IDEType);
}

/**
 * Generate a slug from a string
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

/**
 * Truncate text to a specified length
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength).trim() + '...';
}

/**
 * Build query string from rule filters
 */
export function buildFilterQuery(filters: RuleFilters): string {
  const params = new URLSearchParams();
  
  if (filters.search) params.set('search', filters.search);
  if (filters.ideType) params.set('ideType', filters.ideType);
  if (filters.visibility) params.set('visibility', filters.visibility);
  if (filters.userId) params.set('userId', filters.userId);
  if (filters.tags && filters.tags.length > 0) {
    params.set('tags', filters.tags.join(','));
  }
  
  return params.toString();
}

/**
 * Format date for display
 */
export function formatDate(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

/**
 * Normalized rule metadata interface
 */
export interface NormalizedRuleMetadata {
  title: string;
  description?: string;
  applyType: 'auto' | 'manual' | 'always';
  ideType?: IDEType;
  visibility?: 'PRIVATE' | 'PUBLIC';
  author?: string;
  createdAt?: string;
  updatedAt?: string;
  tags?: string[];
  id?: string;
}

/**
 * Normalized rule content interface
 */
export interface NormalizedRuleContent {
  metadata: NormalizedRuleMetadata;
  content: string;
  rawContent: string;
}

/**
 * Error class for rule normalization failures
 */
export class RuleNormalizationError extends Error {
  constructor(message: string, public field?: string) {
    super(message);
    this.name = 'RuleNormalizationError';
  }
}

/**
 * Download content from a URL
 */
async function downloadContent(url: string): Promise<string> {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch content: ${response.status} ${response.statusText}`);
    }
    return await response.text();
  } catch (error) {
    throw new RuleNormalizationError(
      `Failed to download content from URL: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

/**
 * Read content from a file path (Node.js environment)
 */
async function readFileContent(filePath: string): Promise<string> {
  try {
    // Check if we're in a Node.js environment
    if (typeof window !== 'undefined') {
      throw new Error('File reading is not supported in browser environment');
    }

    const fs = await import('fs/promises');
    return await fs.readFile(filePath, 'utf-8');
  } catch (error) {
    throw new RuleNormalizationError(
      `Failed to read file: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

/**
 * Parse frontmatter from markdown content
 */
function parseFrontmatter(content: string): { metadata: Record<string, any>; content: string } {
  const frontmatterRegex = /^---\s*\n([\s\S]*?)\n---\s*\n([\s\S]*)$/;
  const match = content.match(frontmatterRegex);

  if (!match) {
    return { metadata: {}, content: content.trim() };
  }

  const [, frontmatterStr, markdownContent] = match;
  const metadata: Record<string, any> = {};

  // Parse YAML-like frontmatter
  const lines = frontmatterStr.split('\n');
  for (const line of lines) {
    const trimmedLine = line.trim();
    if (!trimmedLine || trimmedLine.startsWith('#')) continue;

    const colonIndex = trimmedLine.indexOf(':');
    if (colonIndex === -1) continue;

    const key = trimmedLine.slice(0, colonIndex).trim();
    let value = trimmedLine.slice(colonIndex + 1).trim();

    // Remove quotes if present
    if ((value.startsWith('"') && value.endsWith('"')) ||
        (value.startsWith("'") && value.endsWith("'"))) {
      value = value.slice(1, -1);
    }

    // Parse arrays (simple format: [item1, item2])
    if (value.startsWith('[') && value.endsWith(']')) {
      const arrayContent = value.slice(1, -1);
      if (arrayContent.trim()) {
        metadata[key] = arrayContent.split(',').map(item =>
          item.trim().replace(/^["']|["']$/g, '')
        );
      } else {
        metadata[key] = [];
      }
    } else {
      metadata[key] = value;
    }
  }

  return { metadata, content: markdownContent.trim() };
}

/**
 * Extract metadata from markdown content using various patterns
 */
function extractMetadataFromContent(content: string): Partial<NormalizedRuleMetadata> {
  const metadata: Partial<NormalizedRuleMetadata> = {};

  // Extract title from first heading
  const titleMatch = content.match(/^#\s+(.+)/m);
  if (titleMatch) {
    metadata.title = titleMatch[1].trim();
  }

  // Extract description from blockquote after title
  const descriptionMatch = content.match(/^#\s+.+\n\n>\s*(.+)/m);
  if (descriptionMatch) {
    metadata.description = descriptionMatch[1].trim();
  }

  // Look for metadata sections in content
  const metadataSection = content.match(/##\s+Metadata\s*\n([\s\S]*?)(?=\n##|\n#|$)/);
  if (metadataSection) {
    const metadataContent = metadataSection[1];

    // Extract IDE Type
    const ideTypeMatch = metadataContent.match(/\*\*IDE Type:\*\*\s*(.+)/);
    if (ideTypeMatch) {
      const ideType = ideTypeMatch[1].trim().toUpperCase();
      if (Object.keys(IDE_TYPES).includes(ideType)) {
        metadata.ideType = ideType as IDEType;
      }
    }

    // Extract Author
    const authorMatch = metadataContent.match(/\*\*Author:\*\*\s*(.+)/);
    if (authorMatch) {
      metadata.author = authorMatch[1].trim();
    }

    // Extract Created date
    const createdMatch = metadataContent.match(/\*\*Created:\*\*\s*(.+)/);
    if (createdMatch) {
      metadata.createdAt = createdMatch[1].trim();
    }

    // Extract Updated date
    const updatedMatch = metadataContent.match(/\*\*Updated:\*\*\s*(.+)/);
    if (updatedMatch) {
      metadata.updatedAt = updatedMatch[1].trim();
    }
  }

  return metadata;
}

/**
 * Validate required metadata fields
 */
function validateMetadata(metadata: NormalizedRuleMetadata): void {
  const errors: string[] = [];

  if (!metadata.title || metadata.title.trim().length === 0) {
    errors.push('Title is required');
  }

  if (metadata.title && metadata.title.length > 100) {
    errors.push('Title must be 100 characters or less');
  }

  if (metadata.description && metadata.description.length > 500) {
    errors.push('Description must be 500 characters or less');
  }

  if (!metadata.applyType) {
    errors.push('Apply type is required');
  }

  if (metadata.applyType && !['auto', 'manual', 'always'].includes(metadata.applyType)) {
    errors.push('Apply type must be one of: auto, manual, always');
  }

  if (metadata.ideType && !Object.keys(IDE_TYPES).includes(metadata.ideType)) {
    errors.push(`IDE type must be one of: ${Object.keys(IDE_TYPES).join(', ')}`);
  }

  if (metadata.visibility && !['PRIVATE', 'PUBLIC'].includes(metadata.visibility)) {
    errors.push('Visibility must be either PRIVATE or PUBLIC');
  }

  if (errors.length > 0) {
    throw new RuleNormalizationError(`Validation failed: ${errors.join(', ')}`);
  }
}

/**
 * Normalize markdown content for AI Rules
 *
 * @param input - Markdown content as string, URL, or file path
 * @param options - Optional configuration for normalization
 * @returns Promise<NormalizedRuleContent> - Normalized rule content with metadata
 *
 * @example
 * ```typescript
 * // From string content
 * const result = await normalizeMarkdownContent(`
 * ---
 * title: My Rule
 * description: A sample rule
 * ---
 * # My Rule
 * This is the content.
 * `);
 *
 * // From URL
 * const result = await normalizeMarkdownContent('https://example.com/rule.md');
 *
 * // From file path (Node.js only)
 * const result = await normalizeMarkdownContent('./rules/my-rule.md');
 * ```
 */
export async function normalizeMarkdownContent(
  input: string,
  options: {
    defaultApplyType?: 'auto' | 'manual' | 'always';
    defaultIdeType?: IDEType;
    defaultVisibility?: 'PRIVATE' | 'PUBLIC';
    validateRequired?: boolean;
  } = {}
): Promise<NormalizedRuleContent> {
  const {
    defaultApplyType = 'manual',
    defaultIdeType = 'GENERAL',
    defaultVisibility = 'PRIVATE',
    validateRequired = true,
  } = options;

  let rawContent: string;

  try {
    // Determine input type and get content
    if (!input || input.trim().length === 0) {
      throw new RuleNormalizationError('Input cannot be empty');
    }

    // Check if input is a URL
    if (input.startsWith('http://') || input.startsWith('https://')) {
      rawContent = await downloadContent(input);
    }
    // Check if input is a file path (contains file extension or path separators)
    else if (input.includes('/') || input.includes('\\') || input.includes('.')) {
      rawContent = await readFileContent(input);
    }
    // Otherwise treat as direct markdown content
    else {
      rawContent = input;
    }

    if (!rawContent || rawContent.trim().length === 0) {
      throw new RuleNormalizationError('Content cannot be empty after processing');
    }

    // Parse frontmatter and content
    const { metadata: frontmatterMetadata, content: markdownContent } = parseFrontmatter(rawContent);

    // Extract additional metadata from content
    const contentMetadata = extractMetadataFromContent(markdownContent);

    // Merge metadata with precedence: frontmatter > content extraction > defaults
    const mergedMetadata: NormalizedRuleMetadata = {
      applyType: frontmatterMetadata.applyType || defaultApplyType,
      ideType: frontmatterMetadata.ideType || contentMetadata.ideType || defaultIdeType,
      visibility: frontmatterMetadata.visibility || defaultVisibility,
      title: frontmatterMetadata.title || contentMetadata.title || '',
      description: frontmatterMetadata.description || contentMetadata.description,
      author: frontmatterMetadata.author || contentMetadata.author,
      createdAt: frontmatterMetadata.createdAt || contentMetadata.createdAt,
      updatedAt: frontmatterMetadata.updatedAt || contentMetadata.updatedAt,
      tags: frontmatterMetadata.tags || [],
      id: frontmatterMetadata.id,
    };

    // Validate metadata if required
    if (validateRequired) {
      validateMetadata(mergedMetadata);
    }

    // Clean and process the content
    const processedContent = markdownContent.trim();

    return {
      metadata: mergedMetadata,
      content: processedContent,
      rawContent,
    };

  } catch (error) {
    if (error instanceof RuleNormalizationError) {
      throw error;
    }

    throw new RuleNormalizationError(
      `Failed to normalize markdown content: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

/**
 * Format relative time (e.g., "2 hours ago")
 */
export function formatRelativeTime(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);
  
  if (diffInSeconds < 60) return 'just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
  
  return formatDate(d);
}
