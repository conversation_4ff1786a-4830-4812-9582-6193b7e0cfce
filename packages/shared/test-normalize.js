/**
 * Simple test script to validate the normalizeMarkdownContent function
 */

const { normalizeMarkdownContent, RuleNormalizationError } = require('./lib/utils');

async function runTests() {
  console.log('🧪 Testing normalizeMarkdownContent function...\n');

  // Test 1: Basic frontmatter parsing
  console.log('Test 1: Basic frontmatter parsing');
  try {
    const input1 = `---
title: "Test Rule"
description: "A test rule"
applyType: "manual"
ideType: "CURSOR"
visibility: "PUBLIC"
tags: ["test", "example"]
---

# Test Rule

> A test rule

## Instructions
Follow these steps.
`;

    const result1 = await normalizeMarkdownContent(input1);
    console.log('✅ Success!');
    console.log('  Title:', result1.metadata.title);
    console.log('  Apply Type:', result1.metadata.applyType);
    console.log('  IDE Type:', result1.metadata.ideType);
    console.log('  Tags:', result1.metadata.tags);
    console.log('  Content length:', result1.content.length, 'chars\n');
  } catch (error) {
    console.log('❌ Failed:', error.message, '\n');
  }

  // Test 2: Content extraction without frontmatter
  console.log('Test 2: Content extraction without frontmatter');
  try {
    const input2 = `# React Best Practices

> Guidelines for React development

## Metadata

- **IDE Type:** GENERAL
- **Author:** Jane Doe
- **Created:** 8/3/2025

## Guidelines
1. Use hooks
2. Follow patterns
`;

    const result2 = await normalizeMarkdownContent(input2);
    console.log('✅ Success!');
    console.log('  Title:', result2.metadata.title);
    console.log('  Description:', result2.metadata.description);
    console.log('  IDE Type:', result2.metadata.ideType);
    console.log('  Author:', result2.metadata.author, '\n');
  } catch (error) {
    console.log('❌ Failed:', error.message, '\n');
  }

  // Test 3: Error handling - missing title
  console.log('Test 3: Error handling - missing title');
  try {
    const input3 = `---
description: "No title"
---

Some content without title.
`;

    await normalizeMarkdownContent(input3);
    console.log('❌ Should have failed!\n');
  } catch (error) {
    if (error instanceof RuleNormalizationError) {
      console.log('✅ Correctly caught error:', error.message, '\n');
    } else {
      console.log('❌ Unexpected error:', error.message, '\n');
    }
  }

  // Test 4: Skip validation
  console.log('Test 4: Skip validation');
  try {
    const input4 = `Some basic content without proper structure.`;

    const result4 = await normalizeMarkdownContent(input4, { 
      validateRequired: false,
      defaultApplyType: 'always'
    });
    console.log('✅ Success with validation disabled!');
    console.log('  Title:', result4.metadata.title || '(empty)');
    console.log('  Apply Type:', result4.metadata.applyType, '\n');
  } catch (error) {
    console.log('❌ Failed:', error.message, '\n');
  }

  // Test 5: Blob creation (as mentioned in requirements)
  console.log('Test 5: Blob creation');
  try {
    const input5 = `---
title: "API Rule"
description: "API guidelines"
applyType: "auto"
---

# API Rule

Guidelines for API development.
`;

    const result5 = await normalizeMarkdownContent(input5);
    
    // Create Blob from the processed content
    const blob = new Blob([result5.content], { type: 'text/markdown' });
    console.log('✅ Blob created successfully!');
    console.log('  Blob size:', blob.size, 'bytes');
    console.log('  Blob type:', blob.type);
    console.log('  Rule title:', result5.metadata.title, '\n');
  } catch (error) {
    console.log('❌ Failed:', error.message, '\n');
  }

  console.log('🎉 All tests completed!');
}

// Run the tests
runTests().catch(console.error);
